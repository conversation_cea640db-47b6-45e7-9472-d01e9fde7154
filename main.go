// main.go
package main

import (
	"context"
	"fmt"
	"log"
	"os"
	"time"

	"github.com/chromedp/chromedp"
)

func main() {
	opts := append(chromedp.DefaultExecAllocatorOptions[:],
	chromedp.Flag("headless", false),
	chromedp.Flag("disable-gpu", false), // 在某些Windows系统上需要开启GPU
	chromedp.Flag("enable-automation", false),
)
	allocCtx, cancel := chromedp.NewExecAllocator(context.Background(), opts...)
	defer cancel()
	fmt.Println("allocCtx")
	time.Sleep(time.Second * 3)
	// create context
	ctx, cancel := chromedp.NewContext(allocCtx)
	defer cancel()

	fmt.Println("NewContext")
	time.Sleep(time.Second * 5)
	// create a timeout
	ctx, cancel = context.WithTimeout(ctx, 30*time.Second)
	defer cancel()

	fmt.Println("WithTimeout")
	time.Sleep(time.Second * 5)
	var buf []byte
	// capture a screenshot
	if err := chromedp.Run(ctx,
		chromedp.Navigate(`https://bing.com`),
		// wait for 5 seconds
		chromedp.Sleep(5*time.Second),
		chromedp.CaptureScreenshot(&buf),
	); err != nil {
		log.Fatal(err)
	}

	if err := os.WriteFile("time.png", buf, 0644); err != nil {
		log.Fatal(err)
	}
}
